package translate

import (
	"golang.org/x/text/language"
	"testing"
)

func TestTranslate(t *testing.T) {
	rqd := &RequestData{
		OutputLang: "ja",
		Text:       "竿组未搭配完成，请选择合适的配件",
	}
	asterStr := Translate(rqd)

	t.Logf("翻译为%s: %s", rqd.OutputLang, asterStr)
}

func TestTranslateExcel(t *testing.T) {
	ExcelHandle("xlsx/language_info.xlsx", "LanguageInfo", language.Japanese.String())
}

func TestYouDaoTranslation(t *testing.T) {
	GoogleTranslate(language.English.String(), "我爱钓鱼")
}
