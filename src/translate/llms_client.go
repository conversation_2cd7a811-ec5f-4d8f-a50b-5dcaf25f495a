package translate

import (
	"context"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/ollama"
	"github.com/tmc/langchaingo/prompts"
)

const ollamaAddr = "http://192.168.1.83:11434"
const modelName = "llama3"

type RequestData struct {
	OutputLang string `json:"outputLang"`
	Text       string `json:"text"`
}

func Translate(data *RequestData) string {
	// prompt
	prompt := prompts.NewChatPromptTemplate([]prompts.MessageFormatter{
		// prompts.NewSystemMessagePromptTemplate("你是一个只能翻译文本的翻译引擎，不需要进行解释。只能翻译为{{.outputLang}}", []string{"outputLang"}),
		// prompts.NewHumanMessagePromptTemplate("翻译这段文字到 {{.outputLang}}: {{.text}}", []string{"outputLang", "text"}),
		prompts.NewSystemMessagePromptTemplate("你是一个非常厉害的翻译文本的翻译引擎，不需要进行解释。只能翻译为{{.outputLang}}", []string{"outputLang"}),
		prompts.NewHumanMessagePromptTemplate("翻译到 {{.outputLang}}: {{.text}}", []string{"outputLang", "text"}),
	})

	values := map[string]any{
		"outputLang": data.OutputLang,
		"text":       data.Text,
	}

	messages, err := prompt.FormatMessages(values)
	if err != nil {
		return ""
	}

	// 连接ollama，并且制定模型名
	llm, err := ollama.New(ollama.WithServerURL(ollamaAddr), ollama.WithModel(modelName))
	if err != nil {
		return ""
	}

	// llm内容格式化
	content := []llms.MessageContent{
		llms.TextParts(messages[0].GetType(), messages[0].GetContent()),
		llms.TextParts(messages[1].GetType(), messages[1].GetContent()),
	}
	response, err := llm.GenerateContent(context.Background(), content)

	if err != nil {
		return ""
	}

	if len(response.Choices) <= 0 {
		return ""
	}

	return response.Choices[0].Content
}
