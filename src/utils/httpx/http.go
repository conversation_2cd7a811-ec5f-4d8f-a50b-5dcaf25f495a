package httpx

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/sirupsen/logrus"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
)

func PostBytes(ctx context.Context, postURL string, buf []byte) ([]byte, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	bodyReader := bytes.NewReader(buf)
	req, err := http.NewRequestWithContext(ctx, "POST", postURL, bodyReader)
	if err != nil {
		return nil, err
	}

	rsp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logrus.Error("PostUrl:" + err.Error())
		}
	}(rsp.Body)

	data, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func GetUrl(getURL string) (string, error) {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	cookieJar, _ := cookiejar.New(nil)
	client := &http.Client{Transport: tr, Jar: cookieJar}

	rsp, err := client.Get(getURL)
	if err != nil {
		return "", err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logrus.Warnf("httpx GetUrl : %v", err)
		}
	}(rsp.Body)

	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// PostJSON https post json数据
func PostJSON(url string, body []byte) (string, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return "", err
	}

	rsp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer rsp.Body.Close()

	data, err := io.ReadAll(rsp.Body)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

func GetBackJson(host string, params url.Values) ([]byte, error) {
	query := params.Encode()
	path := fmt.Sprintf("%s?%s", host, query)
	resp, err := http.Get(path)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errCls := Body.Close()
		if errCls != nil {
			logrus.Errorf("GetBackJson http close path:%s err:%s", path, errCls)
		}
	}(resp.Body)

	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logrus.Infof("GetBackJson :%s", s)
	return s, nil
}

func PostBackJsonByMap(host string, params map[string]string) ([]byte, error) {
	reqData, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(host, "application/json", bytes.NewReader(reqData))
	if err != nil {
		logrus.Errorf("post error[%v]", string(reqData))
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errCls := Body.Close()
		if errCls != nil {
			logrus.Errorf("GetBackJson http close path:%s err:%s", host, errCls)
		}
	}(resp.Body)

	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		logrus.Infof("PostBackJsonByMap err :%v", err)
	}

	return s, nil
}
