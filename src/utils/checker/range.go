package checker

import (
	"errors"
	"fmt"
	"github.com/spf13/cast"
)

// RangeValidator 实现范围检查
type RangeValidator struct{}

const (
	RangeStyleInterval = 1 // 区间内
	RangeStyleSpecific = 2 // 指定值
	RangeStyleStrings  = 3 // 字符数组内
)

// Validate 实现范围检查的具体逻辑
func (rv *RangeValidator) Validate(value []string, rules map[string]interface{}) error {
	style := cast.ToInt(rules["style"])
	for _, v := range value {
		switch style {
		case RangeStyleInterval:
			err := rv.validateInterval(v, rules)
			if err != nil {
				return err
			}
		case RangeStyleSpecific:
			err := rv.validateSpecific(v, rules)
			if err != nil {
				return err
			}
		case RangeStyleStrings:
			err := rv.validateStrings(v, rules)
			if err != nil {
				return err
			}
		default:
			return errors.New("无效的范围检查样式")
		}
	}
	return nil
}

// validateInterval 验证值是否在指定区间内
func (rv *RangeValidator) validateInterval(v string, rules map[string]interface{}) error {
	minValue := cast.ToInt64(rules["min"])
	maxValue := cast.ToInt64(rules["max"])
	value := cast.ToInt64(v)
	if value < minValue || value > maxValue {
		return fmt.Errorf("值 %v 不在区间 [%v, %v] 内", value, minValue, maxValue)
	}
	return nil
}

// validateSpecific 验证值是否在指定值集合内
func (rv *RangeValidator) validateSpecific(v string, rules map[string]interface{}) error {
	inValues := cast.ToIntSlice(rules["in"])
	temp := cast.ToInt(v)
	found := false
	for _, val := range inValues {
		if temp == val {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("值 %v 不在指定值集合 %v 内", v, inValues)
	}
	return nil
}

// validateStrings 验证值是否在指定字符串集合内
func (rv *RangeValidator) validateStrings(v string, rules map[string]interface{}) error {
	strings := cast.ToStringSlice(rules["strings"])
	found := false
	for _, str := range strings {
		if str == v {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("值 %v 不在指定字符串集合 %v 内", v, strings)
	}
	return nil
}
