package checker

import (
	"encoding/json"
	"fmt"
	"tcloud/wolong/model"
)

// 定义校验类型的常量
const (
	CheckTypeRange      = 1 // 范围检查
	CheckTypeDuplicate  = 2 // 重复性检查
	CheckTypeLoot       = 3 // Loot检查
	CheckTypeTimeSeries = 5 // 时间序列约束检查
	CheckTypeForeignKey = 6 // 外键检查
	CheckTypeBasic      = 8 // 基础检查
)

// Validator 接口定义了一个通用的校验方法
type Validator interface {
	Validate(value []string, rules map[string]interface{}) error
}

// ValidateField 根据给定的检查规则对字段进行校验
func ValidateField(check model.Check, tableName, fieldName string, column, fkColumns []string) error {
	var validator Validator
	var err error

	// 根据检查类型选择相应的校验器
	switch check.CheckType {
	case CheckTypeRange:
		validator = &RangeValidator{} // 范围检查器
	case CheckTypeDuplicate:
		validator = &DuplicateValidator{} // 重复性检查器
	case CheckTypeLoot:
		validator = &LootValidator{} // Loot检查器
	case CheckTypeTimeSeries:
		validator = &TimeValidator{} // 时间序列约束检查器
	case CheckTypeForeignKey:
		validator = &ForeignKeyValidator{fkColumns} // 外键检查器
	case CheckTypeBasic:
		validator = &BasicValidator{} // 基础检查器
	default:
		return fmt.Errorf("未知的检查类型 %d 对于字段 %s 在表 %s", check.CheckType, fieldName, tableName)
	}

	// 解析参数 JSON
	var paramMap map[string]interface{}
	err = json.Unmarshal([]byte(check.ParamJson), &paramMap)
	if err != nil {
		return fmt.Errorf("解析检查 ID %d 的参数 JSON 失败: %v", check.CheckID, err)
	}

	// 执行校验
	err = validator.Validate(column, paramMap)
	if err != nil {
		return fmt.Errorf("字段 %s 在表 %s 的校验失败: %v", fieldName, tableName, err)
	}
	return nil
}
