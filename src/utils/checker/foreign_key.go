package checker

import "fmt"

// ForeignKeyValidator 实现外键检查
type ForeignKeyValidator struct {
	fkColumns []string
}

// Validate 实现外键检查的具体逻辑
func (fkv *ForeignKeyValidator) Validate(value []string, rules map[string]interface{}) error {
	fkvMap := make(map[string]struct{})
	for _, v := range fkv.fkColumns {
		if _, ok := fkvMap[v]; !ok {
			fkvMap[v] = struct{}{}
		}
	}

	for _, v := range value {
		if _, ok := fkvMap[v]; !ok {
			return fmt.Errorf("外键值不存在 v=%s rules=%+v", v, rules)
		}
	}

	return nil
}
