package utils

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"io"
	"io/ioutil"
	"net"
	"net/http"
)

// LocalHost 返回localhost地址，127.0.0.1
func LocalHost() string {
	return "127.0.0.1"
}

// LocalIP 本机IPv4地址 相当于Windows执行ifConfig命令返回的IP
func LocalIP() string {
	iFaces, err := net.Interfaces()
	if err != nil {
		return ""
	}
	for _, rFace := range iFaces {
		if rFace.Flags&net.FlagUp == 0 {
			continue // interface down
		}

		if rFace.Flags&net.FlagLoopback != 0 {
			continue // loopback interface
		}

		addrS, err := rFace.Addrs()
		if err != nil {
			return ""
		}

		for _, addr := range addrS {
			ip := getIpFromAddr(addr)
			if ip == nil {
				continue
			}
			return ip.String()
		}
	}
	return ""
}

// DNSParse 域名解析
func DNSParse(domain string) string {
	addr, err := net.ResolveIPAddr("ip", domain)
	if err != nil {
		logrus.Errorf("Resolvtion Error %v", err.Error())
	}
	// fmt.Println("Resolved address is", addr.String())
	return addr.String()
}

// PublicIP 获取公网，或运营商IP
func PublicIP() string {
	// 获取外网 IP
	responseClient, errClient := http.Get("http://myexternalip.com/raw")
	if errClient != nil {
		return ""
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {

		}
	}(responseClient.Body)

	body, _ := ioutil.ReadAll(responseClient.Body)

	clientIP := fmt.Sprintf("%s", string(body))
	return clientIP
}

func getIpFromAddr(addr net.Addr) net.IP {
	var ip net.IP
	switch v := addr.(type) {
	case *net.IPNet:
		ip = v.IP
	case *net.IPAddr:
		ip = v.IP
	}

	if ip == nil || ip.IsLoopback() {
		return nil
	}

	ip = ip.To4()
	if ip == nil {
		return nil // not an ipv4 address
	}

	return ip
}
