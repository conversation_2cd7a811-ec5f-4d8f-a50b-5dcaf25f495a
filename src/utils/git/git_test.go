package git

import "testing"

func TestGetBranch(t *testing.T) {
	branchName, err := GetCurrentBranch()
	if err != nil {
		return
	}
	t.Log("branch:", branchName)
}

func TestGetGitUserName(t *testing.T) {
	gitUserName, err := GetGitUserName()
	if err != nil {
		return
	}
	t.Log("gitUserName:", gitUserName)
}

func TestGetLastCommit(t *testing.T) {
	gitUserName, err := GetLatestCommit()
	if err != nil {
		return
	}
	t.Log("GetLatestCommit:", gitUserName)
}
