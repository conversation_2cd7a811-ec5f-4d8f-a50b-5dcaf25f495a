package git

import (
	"bytes"
	"errors"
	"os/exec"
	"strings"
	"tcloud/wolong/conf"

	"github.com/sirupsen/logrus"
)

const (
	Dev  = "develop"
	Test = "test"
	Pre  = "pre"
	Prod = "prod"
)

func GetCurrentBranch() (string, error) {
	cmd := exec.Command("git", "rev-parse", "--abbrev-ref", "HEAD")
	out, err := cmd.CombinedOutput()
	if err != nil {
		// 检查是否是因为不在Git仓库中
		if strings.Contains(string(out), "fatal: not a git repository") {
			return "", errors.New("当前目录不是一个Git仓库")
		}
		return "", errors.New("获取分支失败")
	}

	return strings.TrimSpace(string(out)), nil
}

func GetEnvTypeByGitBranch() int {
	envType := 0
	platInfo, err := GetPlatformByEnv()
	if err != nil {
		logrus.Error("getEnv fail:%s", err.Error())
		return envType
	}
	return platInfo.EnvType
}

func GetGitUserName() (string, error) {
	cmd := exec.Command("git", "config", "--global", "user.name")
	userNameBytes, err := cmd.Output()
	if err != nil {
		return "", err
	}
	userName := string(userNameBytes)
	return strings.TrimSpace(userName), nil
}

// GetLatestCommit 获取最新提交记录
func GetLatestCommit() (string, error) {
	// cmd := exec.Command("git", "log", "-1", "--pretty=format:%H %an %ad %s", "--date=short")
	cmd := exec.Command("git", "log", "-1", "--pretty=format:%H(%an于%ad >> %s)", "--date=short")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return "", err
	}
	return out.String(), nil
}

func GetPlatformByEnv() (*conf.PlatformInfo, error) {
	branchName, err := GetCurrentBranch()
	cfg := &conf.PlatformInfo{}
	if err != nil {
		return cfg, err
	}

	for _, info := range conf.SysCfg.PlatformInfo {
		if info.Env == branchName {
			cfg = info
			break
		}
	}

	if cfg.Env == "" {
		return cfg, errors.New("没有读取当前分支对应的配置信息")
	}

	return cfg, nil
}
