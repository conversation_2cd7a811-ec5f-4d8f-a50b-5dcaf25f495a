package utils

import (
	"errors"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"strconv"
	"tcloud/wolong/conf"
	"time"
)

func GenCustomTime(exTime string) (unixStr string, err error) {

	date, err := excelize.ExcelDateToTime(cast.ToFloat64(exTime), false)
	if err != nil {
		return
	}

	str1 := date.Format(time.DateTime)
	// 创建配置的时区
	customLoc := time.FixedZone("Custom", conf.SysCfg.OffsetSeconds)

	var t time.Time
	t, err = time.ParseInLocation(time.DateTime, str1, customLoc)
	if err != nil {
		return
	}
	unix := t.Unix()
	if unix <= 0 {
		return "", errors.New("生成时间戳小于等于0")
	}

	return strconv.FormatInt(unix, 10), nil
}
