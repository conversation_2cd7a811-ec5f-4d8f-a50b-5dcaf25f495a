package utils

import (
	"fmt"
	"os"
	"reflect"
	"strings"
	"tcloud/wolong/utils/plog"
)

func showStruct(value reflect.Value) {
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}
	fmt.Printf("[showStruct]")
	for i := 0; i < value.NumField(); i++ {
		fmt.Printf("%#v ", value.Field(i).Interface())
	}
	fmt.Printf("\n")
}

func showSlice(value reflect.Value) {
	fmt.Printf("[showSlice]")
	for i := 0; i < value.Len(); i++ {
		fmt.Printf("%#v ", value.Index(i).Interface())
	}
	fmt.Printf("\n")
}

func PathCheckDone(path string) error {
	// 校验outputDir是否存在
	if err := os.MkdirAll(path, 0777); err != nil {
		plog.Errorf("创建目录%v失败%v\n", path, err)
		return err
	}
	return nil
}

func IsFileExist(filePath string) bool {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return false
	}
	return true
}

// 字段类型转换
func MemberType(typName string) reflect.Type {
	typeName := strings.ToUpper(typName)
	switch typeName {
	case "BOOL":
		return reflect.TypeOf(true)

	case "UINT":
		return reflect.TypeOf(uint(0))

	case "INT":
		return reflect.TypeOf(int(0))

	case "INT32":
		return reflect.TypeOf(int32(0))

	case "INT64":
		return reflect.TypeOf(int64(0))

	case "STRING":
		return reflect.TypeOf("")

	case "FLOAT":
		return reflect.TypeOf(float32(0))

	case "DOUBLE":
		return reflect.TypeOf(float64(0))
	case "DATE":
		return reflect.TypeOf(int64(0))

	default:
		return reflect.TypeOf(int(0))
	}

}
