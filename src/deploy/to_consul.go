package deploy

import (
	"fmt"
	"tcloud/wolong/conf"
	"tcloud/wolong/utils/consul"
	"tcloud/wolong/utils/plog"
)

var c *consul.Config

func InitConsul() {
	var err error
	consulPath := conf.SysCfg.ConsulAddress
	plog.Infof("配置Consul地址：%s\n", consulPath)
	c = consul.NewConfig(
		consul.WithPrefix("fancy"),
		consul.WithAddress(consulPath))
	err = c.Init()
	if err != nil {
		plog.Fatal(err)
	}
}

func Publish2Consul(channelID int32, tableName string, b []byte) {
	consulPrefix := conf.SysCfg.ConsulPrefix
	k := fmt.Sprintf("%s/%d/%d/%s", consulPrefix, conf.SysCfg.ProductId, channelID, tableName)
	errPut := c.<PERSON><PERSON><PERSON>(k, b)
	if errPut != nil {
		c.<PERSON><PERSON>.Error("err : %v", errPut)
	}
}
