package deploy

import (
	"encoding/json"
	"errors"
	"os"
	"tcloud/wolong/conf"
	"tcloud/wolong/model"
	"tcloud/wolong/utils/git"
	"tcloud/wolong/utils/httpx"
	"tcloud/wolong/utils/plog"
)

const (
	restApiUpload   = "/api/v1/config/batch-upload"
	restApiGetCheck = "/api/v1/config/t-config-check-wolong"
)

func InitMysqlCfg() {
	pltInfo, err := getPlatformByEnv()
	if err != nil {
		plog.Errorf("没有对应分支平台配置信息：%s\n", err)
		// os.Exit(1)
	}
	pltPath := pltInfo.Address
	token := pltInfo.Token
	plog.Infof("平台地址：%s, token: %s\n", pltPath, token)
}

func getPlatformByEnv() (*conf.PlatformInfo, error) {
	branchName, err := git.GetCurrentBranch()
	cfg := &conf.PlatformInfo{}
	if err != nil {
		return cfg, err
	}

	for _, info := range conf.SysCfg.PlatformInfo {
		if info.Env == branchName {
			cfg = info
			break
		}
	}

	if cfg.Env == "" {
		return cfg, errors.New("没有读取当前分支对应的配置信息")
	}

	return cfg, nil
}

func Upload2Platform(info []model.UploadInfo) {
	pltInfo, err := getPlatformByEnv()
	if err != nil || pltInfo.Env == "" {
		plog.Errorf("没有对应分支平台配置信息：%v\n", err)
		os.Exit(1)
	}
	pltPath := pltInfo.Address + restApiUpload
	// token := pltInfo.Token
	jsonObj, _ := json.Marshal(&info)
	rsp, err := httpx.PostJSON(pltPath, jsonObj)
	if err != nil {
		plog.Errorf("上传失败：%s", err)
		os.Exit(1)
	}
	pltRsp := &model.PltRsp{}
	json.Unmarshal([]byte(rsp), pltRsp)

	if pltRsp.Code != 200 {
		plog.Errorf("上传失败：%v", rsp)
		os.Exit(1)
	}

	plog.Infof("上传平台处理结果%v", rsp)
}

func GetConfigCheck() (res map[int32]model.ConfigCheck) {
	pltInfo, err := getPlatformByEnv()
	if err != nil || pltInfo.Env == "" {
		plog.Errorf("没有对应分支平台配置信息：%v\n", err)
		os.Exit(1)
	}
	pltPath := pltInfo.Address + restApiGetCheck
	rsp, err := httpx.GetUrl(pltPath)
	if err != nil {
		plog.Errorf("获取配置检查失败：%s", err)
		os.Exit(1)
	}
	plog.Infof("获取平台配置检查raw结果%v", rsp)

	checkRsp := &model.ConfigCheckGetRsp{}
	err = json.Unmarshal([]byte(rsp), checkRsp)

	if checkRsp.Code != 200 {
		plog.Errorf("获取配置检查失败：%v", rsp)
		os.Exit(1)
	}

	res = checkRsp.Data.ConfigChecks

	return
}
