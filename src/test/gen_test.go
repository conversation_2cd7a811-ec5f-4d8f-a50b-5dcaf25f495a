package test

import (
	"fmt"
	"reflect"
	"tcloud/wolong/utils/plog"
	"testing"

	"github.com/dave/jennifer/jen"
)

// 定义类型
// 根据类型的反射定义来生成结构定义文件

type CCC struct {
	Data  int
	Dict  map[int]int
	Array []string
}

type BBB struct {
	Data  int
	Dict  map[int]string
	Array []int
	Ccc   CCC
}

type AAA struct {
	Int    int
	String string
	Bbb    BBB
	Enable bool
	Money  float64
	Dict   map[int]string
	Array  []int
}

func TestLog(t *testing.T) {
	plog.InitLog("app.log", plog.LogTrace)

	var x interface{} = &AAA{}
	c := NewCoder("equip")
	c.GenStruct(x)
	c.Output(false)
}

type Coder struct {
	jfile    *jen.File
	fileName string
}

func NewCoder(fileName string) *Coder {
	c := new(Coder)
	c.fileName = fileName + "_cfg.go"
	c.jfile = jen.NewFile("cfg")
	c.jfile.HeaderComment("// Code generated by paladin. DO NOT EDIT.")
	return c
}

func (p *Coder) TestPtr(obj interface{}) {
	v := reflect.ValueOf(obj)
	elem := v.Elem()
	fmt.Printf("%#v\n", elem)
	t := reflect.TypeOf(obj)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	for i := 0; i < elem.NumField(); i++ {
		fmt.Printf("%#v %#v %#v\n", elem.Field(i), t.Field(i).Name, t.Field(i).Type.Name())
	}
	subValue := elem.Field(2)
	for i := 0; i < subValue.NumField(); i++ {
		fmt.Printf("subv %#v\n", subValue.Field(i))
	}
	subType := t.Field(2).Type
	for i := 0; i < subType.NumField(); i++ {
		fmt.Printf("subt %#v %v\n", subType.Field(i).Name, subType.Field(i).Type.Name())
	}
}

func (p *Coder) GenStruct(obj interface{}) {
	t := reflect.TypeOf(obj)
	for t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	p.genType(t, "")
	p.genValue(t)
	p.genGet(t)
	p.genGetAll(t)
}

func (p *Coder) genType(t reflect.Type, printPrefix string) {
	fmt.Printf("%s[gen type %s]\n", printPrefix, t.Name())
	fields := make([]jen.Code, t.NumField())
	for i := 0; i < t.NumField(); i++ {
		fmt.Printf("%sfield %d %s %s\n", printPrefix, i, t.Field(i).Name, t.Field(i).Type.Kind().String())
		fields[i] = p.AppendKeyword(jen.Id(t.Field(i).Name), t.Field(i).Type)
	}
	p.jfile.Type().Id(t.Name()).Struct(fields...)
}

// 生成变量声明代码
func (p *Coder) genValue(t reflect.Type) {
	p.jfile.Var().Id("tbl" + t.Name()).Index().Id("*" + t.Name())
}

// 生成GetXXX(id) *XXX
func (p *Coder) genGet(t reflect.Type) {
	p.jfile.Func().Id("Get" + t.Name()).Params(jen.Id("id").Int64()).Id("*" + t.Name()).Block(
		jen.Return().Id("tbl" + t.Name()).Index(jen.Id("id")),
	)
}

// 生成GetAllXXX() []*XXX
func (p *Coder) genGetAll(t reflect.Type) {
	p.jfile.Func().Id("GetAll" + t.Name()).Params().Index().Id("*" + t.Name()).Block(
		jen.Return().Id("tbl" + t.Name()),
	)
}

// 输出
func (p *Coder) Output(writeFile bool) {
	if writeFile {
		if err := p.jfile.Save(p.fileName); err != nil {
			plog.Error(err)
		}
	} else {
		fmt.Printf("\n\n%#v", p.jfile)
	}
}

// 反射类型转换为jennifer语句
func (p *Coder) TypeToJenStatement(t reflect.Type) *jen.Statement {
	switch t.Kind() {
	case reflect.Bool:
		return jen.Bool()

	case reflect.Int:
		return jen.Int()

	case reflect.String:
		return jen.String()

	case reflect.Float32:
		return jen.Float32()

	case reflect.Float64:
		return jen.Float64()

	default:
		return jen.String()
	}
}

func (p *Coder) AppendKeyword(code *jen.Statement, t reflect.Type) *jen.Statement {
	switch t.Kind() {
	case reflect.Bool:
		return code.Bool()

	case reflect.Float32:
		return code.Float32()

	case reflect.Float64:
		return code.Float64()

	case reflect.Int:
		return code.Int()

	case reflect.String:
		return code.String()

	case reflect.Struct:
		p.genType(t, "  ")
		return code.Id(t.Name())

	case reflect.Map:
		return p.AppendKeyword(code.Map(p.TypeToJenStatement(t.Key())), t.Elem())

	case reflect.Slice:
		return p.AppendKeyword(code.Index(), t.Elem())

	default:
		plog.Panic("not support type", t)
		return nil
	}
}

func (p *Coder) structNameToValueName(structName string) (valueName string) {
	if structName == "" {
		return ""
	}
	bs := make([]byte, 0, len(structName))
	bs = append(bs, structName[0]+32)
	bs = append(bs, structName[1:]...)
	return string(bs)
}
