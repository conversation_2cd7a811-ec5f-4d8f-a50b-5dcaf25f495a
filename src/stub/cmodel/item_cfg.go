// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Item struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	NameLanguage int64  `json:"nameLanguage"`
	UnlockLevel  int32  `json:"unlockLevel"`
	Icon         string `json:"icon"`
	DescLanguage int64  `json:"descLanguage"`
	ExpireTime   int64  `json:"expireTime"`
	Stack        int64  `json:"stack"`
	CategoryId   int32  `json:"categoryId"`
	ItemType     int32  `json:"itemType"`
	SubType      int32  `json:"subType"`
	Mark         string `json:"mark"`
	Price        int64  `json:"price"`
	Quality      int32  `json:"quality"`
}

var lockItem sync.RWMutex
var storeItem sync.Map
var strItem string = "item"

func InitItemCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItem, watchItemFunc)
	return LoadAllItemCfg()
}

func fixKeyItem(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItem)
}
func watchItemFunc(key string, js string) {
	mapItem := make(map[int64]*Item)
	errUnmarshal := json.Unmarshal([]byte(js), &mapItem)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItem.Store(key, mapItem)
}

func GetAllItem(option ...consulconfig.Option) map[int64]*Item {
	fitKey := fixKeyItem(option...)
	store, ok := storeItem.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Item)
		if ok {
			return storeMap
		}
	}
	lockItem.Lock()
	defer lockItem.Unlock()
	store, ok = storeItem.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Item)
		if ok {
			return storeMap
		}
	}
	tblItem := make(map[int64]*Item)
	item_str, err := consulconfig.GetInstance().GetConfig(strItem, option...)
	if item_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_str), &tblItem)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item", errUnmarshal)
		return nil
	}
	storeItem.Store(fitKey, tblItem)
	return tblItem
}

func GetItem(id int64, option ...consulconfig.Option) *Item {
	fitKey := fixKeyItem(option...)
	store, ok := storeItem.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Item)
		if ok {
			return storeMap[id]
		}
	}
	lockItem.Lock()
	defer lockItem.Unlock()
	store, ok = storeItem.Load(fitKey)
	if ok {
		tblItem, ok := store.(*Item)
		if ok {
			return tblItem
		}
	}
	tblItem := make(map[int64]*Item)
	item_str, err := consulconfig.GetInstance().GetConfig(strItem, option...)
	if item_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_str), &tblItem)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item", errUnmarshal)
		return nil
	}
	storeItem.Store(fitKey, tblItem)
	return tblItem[id]
}

func LoadAllItemCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItem, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Item", successChannels)
	return nil
}
