// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ActivityConst struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockActivityConst sync.RWMutex
var storeActivityConst sync.Map
var strActivityConst string = "activity_const"

func InitActivityConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strActivityConst, watchActivityConstFunc)
	return LoadAllActivityConstCfg()
}

func fixKeyActivityConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strActivityConst)
}
func watchActivityConstFunc(key string, js string) {
	store, ok := storeActivityConst.Load(key)
	if !ok {
		store = &ActivityConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeActivityConst.Store(key, store)
}

func GetActivityConst(option ...consulconfig.Option) *ActivityConst {
	fitKey := fixKeyActivityConst(option...)
	store, ok := storeActivityConst.Load(fitKey)
	if ok {
		tblActivityConst, ok := store.(*ActivityConst)
		if ok {
			return tblActivityConst
		}
	}
	lockActivityConst.Lock()
	defer lockActivityConst.Unlock()
	store, ok = storeActivityConst.Load(fitKey)
	if ok {
		tblActivityConst, ok := store.(*ActivityConst)
		if ok {
			return tblActivityConst
		}
	}
	tblActivityConst := &ActivityConst{}
	activity_const_str, err := consulconfig.GetInstance().GetConfig(strActivityConst, option...)
	if activity_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(activity_const_str), &tblActivityConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strActivityConst, errUnmarshal, activity_const_str)
		return nil
	}
	storeActivityConst.Store(fitKey, tblActivityConst)
	return tblActivityConst
}

func LoadAllActivityConstCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strActivityConst, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ActivityConst", successChannels)
	return nil
}
