// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StageRewardsRewards struct {
	ItemId int64 `json:"itemId"`
	Count  int64 `json:"count"`
}

type StageRewards struct {
	Id           int64                 `json:"id"`
	Name         string                `json:"name"`
	Mark         string                `json:"mark"`
	DisplayModel int32                 `json:"displayModel"`
	Rewards      []StageRewardsRewards `json:"rewards"`
}

var lockStageRewards sync.RWMutex
var storeStageRewards sync.Map
var strStageRewards string = "stage_rewards"

func InitStageRewardsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStageRewards, watchStageRewardsFunc)
	return LoadAllStageRewardsCfg()
}

func fixKeyStageRewards(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStageRewards)
}
func watchStageRewardsFunc(key string, js string) {
	mapStageRewards := make(map[int64]*StageRewards)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStageRewards)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStageRewards.Store(key, mapStageRewards)
}

func GetAllStageRewards(option ...consulconfig.Option) map[int64]*StageRewards {
	fitKey := fixKeyStageRewards(option...)
	store, ok := storeStageRewards.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StageRewards)
		if ok {
			return storeMap
		}
	}
	lockStageRewards.Lock()
	defer lockStageRewards.Unlock()
	store, ok = storeStageRewards.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StageRewards)
		if ok {
			return storeMap
		}
	}
	tblStageRewards := make(map[int64]*StageRewards)
	stage_rewards_str, err := consulconfig.GetInstance().GetConfig(strStageRewards, option...)
	if stage_rewards_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stage_rewards_str), &tblStageRewards)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stage_rewards", errUnmarshal)
		return nil
	}
	storeStageRewards.Store(fitKey, tblStageRewards)
	return tblStageRewards
}

func GetStageRewards(id int64, option ...consulconfig.Option) *StageRewards {
	fitKey := fixKeyStageRewards(option...)
	store, ok := storeStageRewards.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StageRewards)
		if ok {
			return storeMap[id]
		}
	}
	lockStageRewards.Lock()
	defer lockStageRewards.Unlock()
	store, ok = storeStageRewards.Load(fitKey)
	if ok {
		tblStageRewards, ok := store.(*StageRewards)
		if ok {
			return tblStageRewards
		}
	}
	tblStageRewards := make(map[int64]*StageRewards)
	stage_rewards_str, err := consulconfig.GetInstance().GetConfig(strStageRewards, option...)
	if stage_rewards_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stage_rewards_str), &tblStageRewards)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stage_rewards", errUnmarshal)
		return nil
	}
	storeStageRewards.Store(fitKey, tblStageRewards)
	return tblStageRewards[id]
}

func LoadAllStageRewardsCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStageRewards, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StageRewards", successChannels)
	return nil
}
