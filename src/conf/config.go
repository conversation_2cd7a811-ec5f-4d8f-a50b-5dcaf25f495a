package conf

import (
	"encoding/json"
	"os"
	"strings"
	"tcloud/wolong/utils/plog"

	"github.com/BurntSushi/toml"
)

const (
	TableTypeServerOnly = "server_only"
	TableTypeClientOnly = "client_only"
)

// DefaultChannel 默认渠道
const DefaultChannel = 1001

var Cfg *Config
var SysCfg *systemCfg

type EnumItem struct {
	Field string
	Table string // 表名
	Sheet string // 子表
}

type LocaleItem struct {
	Field string
	Table string
}

type Table struct {
	Workbook   string     // 工作簿
	Sheet      []string   // 子表
	AutoId     bool       // 自动id todo 未实现
	Horizontal bool       // 是否水平解析
	Output     []string   // 输出类型选项 json, cs, go. 默认全输出
	Constant   bool       // 输出常量表，只取第一行，或列数据
	Type       string     // 表类型 server_only; client_local_read
	Duplicate  bool       // 重复结构, 设置为true
	SrvOnly    bool       // 仅服务端使用, 设置为true
	Enums      []EnumItem // 枚举替换
	MissRepeat bool       // 忽略重复Id校验

}

type FtpInfo struct {
	Address string
	Path    string
	User    string
	Pwd     string
}

type PlatformInfo struct {
	Env     string
	Address string
	Token   string
	EnvType int
}

type ChannelInfo struct {
	Name string
	Id   int32
	Path string
}

type TempleInfo struct {
	Name string
	Path string
}

type systemCfg struct {
	Locale           string // 多语言描述
	CompressSliceMap bool   // 压缩slice/map. 将只有1个字段的slice/map 内联结构展开，成为基本类型slice/map
	SegmentSymbol    string // 片段表达式分割符号
	EnumFile         string // 枚举文件
	LocaleFile       string // 多语言文件
	ConsulAddress    string // Consul地址
	ConsulPrefix     string // Consul上级文件夹
	IgnoreLine       int    // 忽略的头几行
	OffsetSeconds    int    // 时区偏移量(秒)
	ProductId        int    // 产品ID
	FtpInfo          *FtpInfo
	PlatformInfo     []*PlatformInfo // 平台信息
	Template         *TempleInfo     // 根模板路径
	Channel          []ChannelInfo
}

type Config struct {
	Tables map[string]*Table
}

func Init(confFile, systemConfFile string) {

	SysCfg = new(systemCfg)
	_, errSys := toml.DecodeFile(systemConfFile, SysCfg)
	if errSys != nil {
		plog.Panic(errSys)
	}

	Cfg = new(Config)
	_, err := toml.DecodeFile(confFile, Cfg)
	if err != nil {
		plog.Panic(err)
	}

	show()
}

func Filter(fromClient bool) {
	if Cfg == nil || !fromClient {
		return
	}

	// for tblName, tbl := range Cfg.Tables {
	// 	if tbl.Type == TableTypeServerOnly {
	// 		delete(Cfg.Tables, tblName)
	// 		plog.Infof("filter server only table %s", tblName)
	// 	}
	// }
}

func ExportJson(filename string) {
	bs, err := json.Marshal(Cfg)
	if err != nil {
		panic("invalid config file, export config.json failed!! " + err.Error())
	}

	f, err := os.Create(filename)
	if err != nil {
		panic("fail to create config.json!!" + err.Error())
	}

	if _, err = f.Write(bs); err != nil {
		panic("fail to write config to json file!!" + err.Error())
	}
}

var (
	OutputJson = outputDelegate("json")
	OutputGo   = outputDelegate("go")
	OutputCs   = outputDelegate("cs")
)

func outputDelegate(express string) func(tableName string) bool {
	return func(tableName string) bool {
		tbl := Cfg.Tables[tableName]
		if tbl == nil {
			return false
		}
		if len(tbl.Output) == 0 {
			return true
		}
		for _, regx := range tbl.Output {
			if strings.ToLower(regx) == express {
				return true
			}
		}
		return false
	}
}

func show() {
	plog.Infof("[配置表清单]:%+v\n", Cfg)
	for tblName, tbl := range Cfg.Tables {
		plog.Infof("表：%s 解析规则：%+v\n", tblName, tbl)
	}
}
