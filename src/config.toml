# 物品相关配置
[tables.item]
sheet = ["Item"]
workbook = "item.xlsx"
enums = [
    {field = "name_language", table = "language_item"},
    {field = "desc_language", table = "language_item"},
]

# 物品——多语言表
[tables.language_item]
sheet = ["LanguageItem"]
workbook = "item.xlsx"
enums = [
]
table_type= "client_only"

[tables.stage_rewards]
sheet = ["StageRewards"]
workbook = "activity.xlsx"
enums = [
    {field = "item_id", table = "item"},
]
table_type= "shared"


[tables.stages]
sheet = ["Stages"]
workbook = "activity.xlsx"
haslabel = true
enums = [
    {field = "activity_id", table = "activity_const"},
    {field = "stage_rewards", table = "stage_rewards"},
]
table_type= "shared"


[tables.activity_const]
sheet = ["ActivityConst"]
workbook = "activity.xlsx"
constant = true
table_type= "server_only"

