package ftpx

import "testing"

func TestUpload(t *testing.T) {
	localDir := "/Users/<USER>/Documents/mygo/src/wolong/bin/data/"
	baseDir := "configs/"
	ftpServer := &FtpServer{}
	err := ftpServer.Login(
		WithHost("************:21"),
		WithUserName("user00"),
		WithPassword("fancygame8888$"),
		WithBaseDir("configs"))
	if err != nil {
		t.Log(err)
	}
	defer ftpServer.Close()

	basePath, err := ftpServer.c.CurrentDir()
	if err != nil {
		return
	}

	ftpServer.UploadDir(baseDir, localDir, basePath)
}
