package excel

import (
	"bytes"
	"fmt"
	"go/format"
	"os"
	"reflect"
	"sort"
	"tcloud/wolong/cmn"
	"tcloud/wolong/utils/plog"

	"github.com/dave/jennifer/jen"
)

const (
	consulConfigPackage = "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logPackage          = "github.com/sirupsen/logrus"
)

type GoCodeBuilder struct {
	jFile      *jen.File
	codeDir    string
	dataDir    string
	fileName   string
	structName string // 给定的外层结构名, 为解决创建的匿名结构问题
}

func NewGoCodeBuilder(codeDir string, dataDir string, fileName string) *GoCodeBuilder {
	c := new(GoCodeBuilder)
	c.codeDir = codeDir
	c.dataDir = dataDir
	c.fileName = fileName
	c.jFile = jen.NewFile(cmn.FileGoPackage)
	c.jFile.HeaderComment(cmn.FileHeadComment)
	return c
}

func (p *GoCodeBuilder) GenStructWithName(obj interface{}, structName string, isConstTable bool) string {
	p.structName = cmn.CamelName(structName)
	t := reflect.TypeOf(obj)
	for t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	p.genType(t, p.structName, "")
	p.genValue(isConstTable)

	p.genInitConfig()
	p.genFixKey()
	p.genWatch(isConstTable)

	loadFuncName := p.genLoadFile(isConstTable)

	// consul文件按需加载，这里不做处理
	if isConstTable {

	} else {
		p.genGetAll()
	}

	p.genGet(isConstTable)
	// p.genLoadConsulCfg()
	p.genLoadAllConsulCfg()

	// fmt.Println(p.jFile.GoString())

	return loadFuncName
}

func (p *GoCodeBuilder) GenType(t reflect.Type, structName string) {
	p.structName = structName
	for t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	p.genType(t, p.structName, "")
	p.genValue(false)
	p.genGet(false)
	p.genGetAll()
	p.genLoadFile(false)
}

// 这个代码问题还挺多的
func (p *GoCodeBuilder) genType(t reflect.Type, structName string, printPrefix string) {
	// fmt.Printf("%s[gen type %s]\n", printPrefix, t.Name())
	fields := make([]jen.Code, t.NumField())
	for i := 0; i < t.NumField(); i++ {
		subField := t.Field(i)
		// fmt.Printf("%sfield %d %s %s\n", printPrefix+"  ", i, t.Field(i).Name, t.Field(i).Type.Kind().String())
		switch subField.Type.Kind() {
		case reflect.Struct:
			subStruct := structName + subField.Name
			p.genType(subField.Type, subStruct, printPrefix+"  ")
			fields[i] = jen.Id(subField.Name).Id(subStruct).Tag(map[string]string{"json": subField.Tag.Get("json")})

		case reflect.Map:
			elem := subField.Type.Elem()
			if elem.Kind() == reflect.Struct {
				mapSubStruct := structName + subField.Name
				p.genType(elem, mapSubStruct, printPrefix+"  ")
				fields[i] = jen.Id(subField.Name).Map(p.TypeToJenStatement(subField.Type.Key())).Id(mapSubStruct).Tag(map[string]string{"json": subField.Tag.Get("json")})
			} else {
				// 常规类型 int, float, string等
				fields[i] = p.AppendKeyword(jen.Id(subField.Name).Map(p.TypeToJenStatement(subField.Type.Key())), elem).Tag(map[string]string{"json": subField.Tag.Get("json")})
			}

		case reflect.Slice:
			elem := subField.Type.Elem()
			if elem.Kind() == reflect.Struct {
				sliceSubStruct := structName + subField.Name
				p.genType(elem, sliceSubStruct, printPrefix+"  ")
				fields[i] = jen.Id(subField.Name).Index().Id(sliceSubStruct).Tag(map[string]string{"json": subField.Tag.Get("json")})
			} else {
				// 常规类型 int, float, string等
				fields[i] = p.AppendKeyword(jen.Id(subField.Name).Index(), elem).Tag(map[string]string{"json": subField.Tag.Get("json")}) //  Id(elem.Kind().String())
			}

		default:
			// 基础类型
			fields[i] = p.AppendKeyword(jen.Id(subField.Name), subField.Type).Tag(map[string]string{"json": subField.Tag.Get("json")})
		}
	}

	if structName == "" {
		structName = t.Name()
	}

	p.jFile.Type().Id(structName).Struct(fields...)
	if t.Kind() == reflect.Struct {
		p.jFile.Line()
	}
}

// 生成变量声明代码
func (p *GoCodeBuilder) genValue(isConstTable bool) {
	// var jenPar jen.Statement
	// if isConstTable {
	// 	jenPar = *jen.Return().Id("&" + p.structName)
	// } else {
	// 	jenPar = *jen.Return().Map(jen.Int64()).Id("*" + p.structName)
	// }
	// p.jFile.Var().Id("tbl"+p.structName+"Pool").Op("=").Qual("sync", "Pool").Block(
	// 	jen.List(jen.Id("New").Op(":").Func().Params().Id("interface{}").Block(
	// 		jenPar.Block(),
	// 	), jen.Id("")),
	// )

	p.jFile.Var().Id("lock"+p.structName).Qual("sync", "RWMutex")
	p.jFile.Var().Id("store"+p.structName).Qual("sync", "Map")
	p.jFile.Var().Id("str" + p.structName).String().Op("=").Lit(p.fileName)
}

func (p *GoCodeBuilder) genFixKey() {
	p.jFile.Func().Id("fixKey"+p.structName).Params(jen.Id("option... consulconfig.Option")).String().Block(
		jen.Id("opt").Op(":=").Qual(consulConfigPackage, "NewOptions").Call(jen.Id("option...")),
		jen.Return().Qual("fmt", "Sprintf").Call(jen.Lit("%d/%d/%s"), jen.Id("opt").Dot("GetProduct()"), jen.Id("opt").Dot("GetChannel()"), jen.Id("str"+p.structName)),
	)
}

func (p *GoCodeBuilder) genInitConfig() {
	p.jFile.Func().Id("Init"+p.structName+"Cfg").Params().Error().Block(
		jen.Qual(consulConfigPackage, "GetInstance").
			Call().Dot("AddWatchHandler").
			Call(jen.Id("str"+p.structName), jen.Id("watch"+p.structName+"Func")),
		jen.Return().Qual("", "LoadAll"+p.structName+"Cfg").Call(),
	).Line()
}

func (p *GoCodeBuilder) genWatch(isConstTable bool) {
	if isConstTable {
		p.jFile.Func().Id("watch"+p.structName+"Func").Params(jen.Id("key").String(), jen.Id("js").String()).Block(
			jen.List(jen.Id("store"), jen.Id("ok")).Op(":=").Id("store"+p.structName).Dot("Load").Call(jen.Id("key")),
			jen.If(jen.Id("!ok")).Block(
				jen.Id("store").Op("=").Qual("", "&"+p.structName+"{}"),
			),
			jen.Id("errUnmarshal").Op(":=").Qual("encoding/json", "Unmarshal").Call(jen.Id("[]byte(js)"), jen.Id("&store")),
			jen.If(jen.Id("errUnmarshal").Op("!=").Id("nil")).Block(
				jen.Qual(logPackage, "Errorf").Call(jen.List(jen.Lit("fail to unmarshal JSON: %v"), jen.Id("errUnmarshal"))),
				jen.Return().Op(""),
			),
			jen.Id("store"+p.structName).Dot("Store").Call(jen.Id("key"), jen.Id("store")),
		).Line()
	} else {
		p.jFile.Func().Id("watch"+p.structName+"Func").Params(jen.Id("key").String(), jen.Id("js").String()).Block(
			//tblBaitAffinity := make(map[int64]*BaitAffinity, 0)
			jen.Id("map"+p.structName).Op(":=").Qual("", "make").Call(jen.Map(jen.Int64()).Id("*"+p.structName)),
			jen.Id("errUnmarshal").Op(":=").Qual("encoding/json", "Unmarshal").Call(jen.Id("[]byte(js)"), jen.Id("&"+"map"+p.structName)),
			jen.If(jen.Id("errUnmarshal").Op("!=").Id("nil")).Block(
				jen.Qual(logPackage, "Errorf").Call(jen.List(jen.Lit("fail to unmarshal JSON: %v"), jen.Id("errUnmarshal"))),
				jen.Return().Op(""),
			),

			jen.Id("store"+p.structName).Dot("Store").Call(jen.Id("key"), jen.Id("map"+p.structName)),
		).Line()
	}
}

// 生成GetXXX(id) *XXX
func (p *GoCodeBuilder) genGet(isConstTable bool) {
	tblName := "tbl" + p.structName
	fileNameStrVarName := p.fileName + "_str"
	errUnmarshalVarName := "errUnmarshal"
	fitKeyVarName := "fitKey"
	storeVarName := "store"
	storeMapVarName := "storeMap"
	errVarName := "err" // 添加错误变量名

	if isConstTable {
		p.jFile.Func().Id("Get"+p.structName).
			Params(jen.Id("option... consulconfig.Option")).
			Id("*"+p.structName).
			Block(
				jen.Id(fitKeyVarName).Op(":=").Qual("", "fixKey"+p.structName).Call(jen.Id("option...")),
				jen.List(jen.Id(storeVarName), jen.Id("ok")).Op(":=").Id("store"+p.structName).Dot("Load").Call(jen.Id(fitKeyVarName)),
				jen.If(jen.Id("ok")).Block(
					jen.List(jen.Id(tblName), jen.Id("ok")).Op(":=").Id(storeVarName).Dot("(").Id("*"+p.structName).Add().Id(")"),
					jen.If(jen.Id("ok")).Block(
						jen.Return().Id(tblName),
					),
				),

				jen.Id("lock"+p.structName).Dot("Lock").Call(),
				jen.Defer().Id("lock"+p.structName).Dot("Unlock").Call(),

				jen.List(jen.Id(storeVarName), jen.Id("ok")).Op("=").Id("store"+p.structName).Dot("Load").Call(jen.Id(fitKeyVarName)),
				jen.If(jen.Id("ok")).Block(
					jen.List(jen.Id(tblName), jen.Id("ok")).Op(":=").Id(storeVarName).Dot("(").Id("*"+p.structName).Add().Id(")"),
					jen.If(jen.Id("ok")).Block(
						jen.Return().Id(tblName),
					),
				),

				jen.Id(tblName).Op(":=").Qual("", "&"+p.structName).Id("{}"),
				jen.List(jen.Id(fileNameStrVarName), jen.Id(errVarName)).Op(":="). // 添加错误变量
													Qual(consulConfigPackage, "GetInstance").
													Call().Dot("GetConfig").Call(jen.Id("str"+p.structName), jen.Id("option...")),
				jen.If(jen.Id(fileNameStrVarName).Op("==").Lit("").Op("||").Id(errVarName).Op("!=").Id("nil")).Block( // 添加错误检查
					jen.Qual(logPackage, "Errorf").
						Call(jen.List(jen.Lit("fail json.Unmarshal is empty, err:%+v"), jen.Id(errVarName))),
					jen.Return().Id("nil"),
				),
				jen.Id(errUnmarshalVarName).Op(":=").Qual("encoding/json", "Unmarshal").
					Call(jen.Id("[]byte("+fileNameStrVarName+")"), jen.Id("&"+tblName)),
				jen.If(jen.Id(errUnmarshalVarName).Op("!=").Id("nil")).Block(
					jen.Qual(logPackage, "Errorf").
						Call(jen.List(jen.Lit("fail json.Unmarshal %s err: %v, json:%s"), jen.Id("str"+p.structName), jen.Id(errUnmarshalVarName), jen.Id(fileNameStrVarName))),
					jen.Return().Id("nil"),
				),

				jen.Id("store"+p.structName).Dot("Store").Call(jen.Id(fitKeyVarName), jen.Id(tblName)),

				jen.Return().Id(tblName),
			).Line()
	} else {
		p.jFile.Func().Id("Get"+p.structName).
			Params(jen.Id("id").Int64(), jen.Id("option... consulconfig.Option")).
			Id("*"+p.structName).
			Block(
				jen.Id(fitKeyVarName).Op(":=").Qual("", "fixKey"+p.structName).Call(jen.Id("option...")),
				jen.List(jen.Id(storeVarName), jen.Id("ok")).Op(":=").Id("store"+p.structName).Dot("Load").Call(jen.Id(fitKeyVarName)),
				jen.If(jen.Id("ok")).Block(
					jen.List(jen.Id(storeMapVarName), jen.Id("ok")).Op(":=").Id(storeVarName).Dot("(").Map(jen.Int64()).Id("*"+p.structName).Id(")"),
					jen.If(jen.Id("ok")).Block(
						jen.Return().Id(storeMapVarName).Index(jen.Id("id")),
					),
				),

				jen.Id("lock"+p.structName).Dot("Lock").Call(),
				jen.Defer().Id("lock"+p.structName).Dot("Unlock").Call(),

				jen.List(jen.Id(storeVarName), jen.Id("ok")).Op("=").Id("store"+p.structName).Dot("Load").Call(jen.Id(fitKeyVarName)),
				jen.If(jen.Id("ok")).Block(
					jen.List(jen.Id(tblName), jen.Id("ok")).Op(":=").Id(storeVarName).Dot("(").Id("*"+p.structName).Add().Id(")"),
					jen.If(jen.Id("ok")).Block(
						jen.Return().Id(tblName),
					),
				),

				jen.Id(tblName).Op(":=").Qual("", "make").Call(jen.Map(jen.Int64()).Id("*"+p.structName)),
				jen.List(jen.Id(fileNameStrVarName), jen.Id(errVarName)).Op(":="). // 添加错误变量
													Qual(consulConfigPackage, "GetInstance").
													Call().Dot("GetConfig").Call(jen.Id("str"+p.structName), jen.Id("option...")),
				jen.If(jen.Id(fileNameStrVarName).Op("==").Lit("").Op("||").Id(errVarName).Op("!=").Id("nil")).Block( // 添加错误检查
					jen.Qual(logPackage, "Errorf").
						Call(jen.List(jen.Lit("fail json.Unmarshal is empty, err:%+v"), jen.Id(errVarName))),
					jen.Return().Id("nil"),
				),
				jen.Id(errUnmarshalVarName).Op(":=").Qual("encoding/json", "Unmarshal").
					Call(jen.Id("[]byte("+fileNameStrVarName+")"), jen.Id("&"+tblName)),
				jen.If(jen.Id(errUnmarshalVarName).Op("!=").Id("nil")).Block(
					jen.Qual(logPackage, "Errorf").
						Call(jen.List(jen.Lit("fail json.Unmarshal %s err: %v"), jen.Lit(p.fileName), jen.Id(errUnmarshalVarName))),
					jen.Return().Id("nil"),
				),

				jen.Id("store"+p.structName).Dot("Store").Call(jen.Id(fitKeyVarName), jen.Id(tblName)),
				jen.Return().Id(tblName).Index(jen.Id("id")),
			).Line()
	}
}

// 生成GetAllXXX() []*XXX
func (p *GoCodeBuilder) genGetAll() {
	tblName := "tbl" + p.structName
	fileNameStrVarName := p.fileName + "_str"
	errUnmarshalVarName := "errUnmarshal"
	fitKeyVarName := "fitKey"
	storeVarName := "store"
	storeMapVarName := "storeMap"
	errVarName := "err" // 添加错误变量名

	p.jFile.Func().Id("GetAll"+p.structName).Params(jen.Id("option... consulconfig.Option")).Map(jen.Int64()).Id("*"+p.structName).Block(
		jen.Id(fitKeyVarName).Op(":=").Qual("", "fixKey"+p.structName).Call(jen.Id("option...")),
		jen.List(jen.Id(storeVarName), jen.Id("ok")).Op(":=").Id("store"+p.structName).Dot("Load").Call(jen.Id(fitKeyVarName)),
		jen.If(jen.Id("ok")).Block(
			jen.List(jen.Id(storeMapVarName), jen.Id("ok")).Op(":=").Id(storeVarName).Dot("(").Map(jen.Int64()).Id("*"+p.structName).Id(")"),
			jen.If(jen.Id("ok")).Block(
				jen.Return().Id(storeMapVarName),
			),
		),

		jen.Id("lock"+p.structName).Dot("Lock").Call(),
		jen.Defer().Id("lock"+p.structName).Dot("Unlock").Call(),

		jen.List(jen.Id(storeVarName), jen.Id("ok")).Op("=").Id("store"+p.structName).Dot("Load").Call(jen.Id(fitKeyVarName)),
		jen.If(jen.Id("ok")).Block(
			jen.List(jen.Id(storeMapVarName), jen.Id("ok")).Op(":=").Id(storeVarName).Dot("(").Map(jen.Int64()).Id("*"+p.structName).Id(")"),
			jen.If(jen.Id("ok")).Block(
				jen.Return().Id(storeMapVarName),
			),
		),

		jen.Id(tblName).Op(":=").Qual("", "make").Call(jen.Map(jen.Int64()).Id("*"+p.structName)),
		jen.List(jen.Id(fileNameStrVarName), jen.Id(errVarName)).Op(":="). // 添加错误变量
											Qual(consulConfigPackage, "GetInstance").
											Call().Dot("GetConfig").Call(jen.Id("str"+p.structName), jen.Id("option...")),
		jen.If(jen.Id(fileNameStrVarName).Op("==").Lit("").Op("||").Id(errVarName).Op("!=").Id("nil")).Block( // 添加错误检查
			jen.Qual(logPackage, "Errorf").
				Call(jen.List(jen.Lit("fail json.Unmarshal is empty, err:%+v"), jen.Id(errVarName))),
			jen.Return().Id("nil"),
		),
		jen.Id(errUnmarshalVarName).Op(":=").Qual("encoding/json", "Unmarshal").
			Call(jen.Id("[]byte("+fileNameStrVarName+")"), jen.Id("&"+tblName)),
		jen.If(jen.Id(errUnmarshalVarName).Op("!=").Id("nil")).Block(
			jen.Qual(logPackage, "Errorf").
				Call(jen.List(jen.Lit("fail json.Unmarshal %s err: %v"), jen.Lit(p.fileName), jen.Id(errUnmarshalVarName))),
			jen.Return().Id("nil"),
		),
		jen.Id("store"+p.structName).Dot("Store").Call(jen.Id(fitKeyVarName), jen.Id(tblName)),
		jen.Return().Id(tblName)).Line()
}

func (p *GoCodeBuilder) genLoadFile(isConstTable bool) string {
	funcName := "LoadFile" + p.structName
	// var c *jen.Statement
	// if isConstTable {
	// 	// c = jen.Var().Id("tbl" + p.structName).Id("*" + p.structName)
	// 	c = jen.Id("tbl"+p.structName).Op(":= "+"tbl"+p.structName+"Pool.").Qual("", "Get").Call().
	// 		Id(".(*").Id(p.structName).Id(")")
	// } else {
	// 	// c = jen.Var().Id("tbl" + p.structName).Map(jen.Int64()).Id("*" + p.structName)
	// 	c = jen.Id("tbl"+p.structName).Op(":= "+"tbl"+p.structName+"Pool.").Qual("", "Get").Call().
	// 		Id(".(").Map(jen.Int64()).Id("*" + p.structName).Id(")")
	// }

	// p.jFile.Func().Id(funcName).Params().
	// 	Block(
	// 		jen.List(jen.Id("file"), jen.Id("err")).Op(":=").Qual("os", "Open").Call(jen.Lit(p.dataDir+"/"+p.fileName+".json")),
	// 		jen.If(jen.Id("err").Op("!=").Id("nil")).Block(
	// 			jen.Qual("fmt", "Println").Call(jen.List(jen.Lit("fail to open!!"), jen.Id("err"))),
	// 			jen.Return(),
	// 		),
	// 		jen.Id("defer").Op(" ").Id("file").Dot("Close").Call(),
	// 		c,
	// 		jen.Id("decoder").Op(":=").Qual("encoding/json", "NewDecoder").Call(jen.Id("file")),
	// 		jen.Id("err").Op("=").Id("decoder").Dot("Decode").Call(jen.Id("&tbl"+p.structName)),
	// 		jen.If(jen.Id("err").Op("!=").Id("nil")).Block(
	// 			jen.Qual("github.com/sirupsen/logrus", "Errorf").Call(jen.List(jen.Lit("fail to decode!!err: %v"), jen.Id("err"))),
	// 			jen.Return(),
	// 		),
	// 	).Line()
	return funcName
}

func (p *GoCodeBuilder) genLoadConsulCfg() string {
	funcName := "Load" + p.structName + "Cfg"

	p.jFile.Func().Id(funcName).Params(jen.Id("option... consulconfig.Option")).Error().Block(
		jen.List(jen.Id("_"), jen.Id("err")).Op(":=").
			Qual(consulConfigPackage, "GetInstance").
			Call().Dot("GetConfig").Call(jen.Id("str"+p.structName), jen.Id("option...")),
		jen.Return().Id("err"),
	).Line()
	return funcName

	// p.jFile.Func().Id(funcName).Params(jen.Id("option... consulconfig.Option")).
	// 	Block(
	// 		jen.List(jen.Id("_")).Op("=").
	// 			Qual("git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config", "GetInstance").
	// 			Call().Dot("GetConfig").Call(jen.Id("str"+p.structName), jen.Id("option...")),
	// 	).Line()
	// return funcName
}

func (p *GoCodeBuilder) genLoadAllConsulCfg() string {
	funcName := "LoadAll" + p.structName + "Cfg"

	p.jFile.Func().Id(funcName).Params().Error().Block(
		// channels := consulconfig.GetAllChannelMap()
		// jen.Id("channels").Op(":=").Qual(consulConfigPackage, "GetAllChannelMap").Call(),
		jen.Id("channels").Op(":=").Qual(consulConfigPackage, "GetInstance").Call().Dot("GetAllChannel").Call(),

		// successChannels := make([]int32, 0)
		jen.Id("successChannels").Op(":=").Qual("", "make").Call(jen.Index().Int32(), jen.Lit(0)),

		// var wg sync.WaitGroup
		// var mu sync.Mutex
		jen.Var().Id("wg").Qual("sync", "WaitGroup"),
		jen.Var().Id("mu").Qual("sync", "Mutex"),

		// for _, channel := range channels {
		//     wg.Add(1)
		//     go func(chanID int32) { ... }(channel)
		// }
		jen.For(jen.List(jen.Id("_"), jen.Id("channel")).Op(":=").Range().Id("channels")).Block(
			jen.Id("wg").Dot("Add").Call(jen.Lit(1)),
			jen.Go().Func().Params(jen.Id("chanID").Int32()).Block(
				jen.Defer().Id("wg").Dot("Done").Call(),

				jen.List(jen.Id("_"), jen.Id("err")).Op(":=").Qual(consulConfigPackage, "GetInstance").Call().
					Dot("GetConfig").Call(jen.Id("str"+p.structName), jen.Qual(consulConfigPackage, "WithChannel").Call(jen.Id("chanID"))),
				jen.If(jen.Id("err").Op("!=").Nil()).Block(
					jen.Return(),
				),

				jen.Id("mu").Dot("Lock").Call(),
				jen.Id("successChannels").Op("=").Qual("", "append").Call(jen.Id("successChannels"), jen.Id("chanID")),
				jen.Id("mu").Dot("Unlock").Call(),
			).Call(jen.Id("channel")),
		),

		// wg.Wait()
		jen.Id("wg").Dot("Wait").Call(),

		// if len(successChannels) == 0 { return fmt.Errorf(...) }
		jen.If(jen.Len(jen.Id("successChannels")).Op("==").Lit(0)).Block(
			jen.Return().Qual("fmt", "Errorf").Call(jen.Lit("no config loaded")),
		),

		// logrus.Infof(...)
		jen.Qual(logPackage, "Infof").Call(
			jen.Lit("successfully loaded %s config for channels: %v"),
			jen.Lit(p.structName),
			jen.Id("successChannels"),
		),

		// return nil
		jen.Return().Nil(),
	).Line()

	return funcName
}

// GenInit 生成init() { LoadFile(); }
func (p *GoCodeBuilder) GenInit(loadFuncName string) {
	p.jFile.Func().Id("init").Params().Block(
		jen.Qual("", loadFuncName).Call(),
	)
}

func (p *GoCodeBuilder) GenReloadAllFile(funcNames []string) string {
	stmts := make([]jen.Code, 0, len(funcNames))
	sort.Slice(funcNames, func(i, j int) bool {
		return funcNames[i] < funcNames[j]
	})
	for _, name := range funcNames {
		stmt := jen.Qual("", name).Call()
		if stmt == nil {
			continue
		}
		stmts = append(stmts, stmt)
	}
	funcName := "ReloadAllFile"
	p.jFile.Func().Id(funcName).Params().Block(stmts...).Line()
	return funcName
}

// Output 输出
func (p *GoCodeBuilder) Output() {
	filename := p.codeDir + "/" + p.fileName + "_cfg.go"
	buf := &bytes.Buffer{}
	if err := p.jFile.Render(buf); err != nil {
		plog.Error("fail to p.jFile.Render!! ", err)
		return
	}
	var bs []byte
	// if runtime.GOOS == "darwin" {
	// 	bs = bytes.Replace(buf.Bytes(), []byte("\n"), []byte("\r"), -1)
	// } else {
	bs = buf.Bytes()
	// }
	var err error
	bs, err = format.Source(bs)
	if err != nil {
		plog.Error("fail to format.Source!! ", err)
		return
	}
	if err = os.WriteFile(filename, bs, 0644); err != nil {
		plog.Error("fail to ioutil.WriteFile!! ", err)
		return
	}
}

func (p *GoCodeBuilder) DebugType(t reflect.Type, structName string) {
	p.structName = structName
	for t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	p.genType(t, p.structName, "")
	fmt.Printf("%#v\n", p.jFile)
}

// TypeToJenStatement 反射类型转换为jennifer语句
func (p *GoCodeBuilder) TypeToJenStatement(t reflect.Type) *jen.Statement {
	switch t.Kind() {
	case reflect.Bool:
		return jen.Bool()

	case reflect.Int,
		reflect.Int64:
		return jen.Int64()

	case reflect.Int32:
		return jen.Int32()

	case reflect.String:
		return jen.String()

	case reflect.Float32:
		return jen.Float32()

	case reflect.Float64:
		return jen.Float64()

	default:
		return jen.String()
	}
}

func (p *GoCodeBuilder) AppendKeyword(code *jen.Statement, t reflect.Type) *jen.Statement {
	switch t.Kind() {
	case reflect.Bool:
		return code.Bool()

	case reflect.Float32:
		return code.Float32()

	case reflect.Float64:
		return code.Float64()

	case reflect.Int,
		reflect.Int64:
		return code.Int64()

	case reflect.Int32:
		return code.Int32()

	case reflect.String:
		return code.String()

	default:
		plog.Panic("not support type", t)
		return nil
	}
}

func (p *GoCodeBuilder) structNameToValueName(structName string) (valueName string) {
	if structName == "" {
		return ""
	}
	bs := make([]byte, 0, len(structName))
	bs = append(bs, structName[0]+32)
	bs = append(bs, structName[1:]...)
	return string(bs)
}
