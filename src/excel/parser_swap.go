package excel

import (
	"strconv"
	"strings"
	"tcloud/wolong/cmn"
	"tcloud/wolong/conf"
	"tcloud/wolong/utils/plog"
)

// 枚举替换
func (p *Parser) swapEnum(channelID int32, origin [][]string, enumItems []conf.EnumItem) error {
	for _, enumItem := range enumItems {
		if enumItem.Table == "enum" {
			tokens := strings.Split(enumItem.Sheet, ",")
			swapTableList := make([]map[string]string, 0, len(tokens))
			for _, sheet := range tokens {
				swapTable := p.EnumSwapDict[sheet]
				if swapTable == nil {
					plog.Errorf("enum子表%v不存在!!\n", sheet)
					return cmn.ErrNotExist
				}
				swapTableList = append(swapTableList, swapTable)
			}

			if err := p.swapEnumFieldMultiTable(origin, enumItem.Field, swapTableList); err != nil {
				return err
			}
			continue
		}

		tokens := strings.Split(enumItem.Table, ",")
		swapTableList := make([]map[string]string, 0, len(tokens))
		for _, table := range tokens {
			// 其他表单替换
			xlsxInfo, ok := p.ChannelXlsx[channelID][table]
			if !ok || xlsxInfo == nil {
				xlsxInfo = p.Xlsx[table]
			}
			if xlsxInfo == nil || xlsxInfo.NameDict == nil {
				plog.Errorf("xlsx表%v不存在!!不存在name->id键值对!!\n", table)
				return cmn.ErrNotExist
			}
			plog.Infof("枚举表%v替换为%v\n", table, xlsxInfo.NameDict)
			swapTableList = append(swapTableList, xlsxInfo.NameDict)
		}
		if err := p.swapEnumFieldMultiTable(origin, enumItem.Field, swapTableList); err != nil {
			return err
		}

	}
	return nil
}

// 替换枚举列，把数据的field列内容进行替换
func (p *Parser) swapEnumField(origin [][]string, field string, swapTable map[string]string) error {
	var ok bool
	var err error = nil
	var newValue string

	column := 0
	for ; column < len(origin[0]); column++ {
		if origin[1][column] != field {
			continue
		}

		for rowIdx := 0; rowIdx < len(origin); rowIdx++ {
			// 前ignoreLine行是结构，不替换
			if rowIdx < conf.SysCfg.IgnoreLine {
				continue
			}
			if strings.ToUpper(origin[rowIdx][column]) == "NULL" {
				continue
			}
			// 原本就已经是数字了，不需要枚举
			_, e := strconv.ParseInt(origin[rowIdx][column], 10, 64)
			if e == nil {
				continue
			}

			newValue, ok = swapTable[origin[rowIdx][column]]
			if ok == false {
				plog.Errorf("枚举值%v不存在 第%d行第%d列\n", origin[rowIdx][column], rowIdx, column)
				err = cmn.ErrFail
				continue
			}
			origin[rowIdx][column] = newValue
		}
	}
	return err
}

func (p *Parser) swapEnumFieldMultiTable(origin [][]string, field string, swapTableList []map[string]string) error {
	var ok bool
	var err error = nil
	var newValue string

	for column := 0; column < len(origin[0]); column++ {
		if origin[1][column] != field {
			continue
		}

		for rowIdx := 0; rowIdx < len(origin); rowIdx++ {
			// 前ignoreLine行是结构，不替换
			if rowIdx < conf.SysCfg.IgnoreLine {
				continue
			}

			// 原本就是NULL
			if strings.ToUpper(origin[rowIdx][column]) == "NULL" {
				continue
			}

			// 原本就已经是数字了，不需要枚举
			_, e := strconv.ParseInt(origin[rowIdx][column], 10, 64)
			if e == nil {
				continue
			}

			for _, swapTable := range swapTableList {
				newValue, ok = swapTable[origin[rowIdx][column]]
				if ok {
					origin[rowIdx][column] = newValue
					goto NextRowId
				}
			}

			plog.Errorf("枚举值%v不存在 %d行-%d列,请检查第四行是否配置Array标签\n", origin[rowIdx][column], rowIdx, column)
			err = cmn.ErrFail
		NextRowId:
		}
	}
	return err
}

// 多语言替换
func (p *Parser) swapLocale(origin [][]string, LocaleItems []conf.LocaleItem, locale string) error {
	for _, localeItem := range LocaleItems {
		localeSwapTable := p.LocaleSwapDict[localeItem.Table]
		if localeSwapTable == nil {
			plog.Errorf("多语言替换子表%v不存在!!\n", localeItem.Table)
			return cmn.ErrNotExist
		}
		swapTable := localeSwapTable[locale]
		if swapTable == nil {
			plog.Errorf("多语言替换表%v不存在语言%v!!\n", localeItem.Table, locale)
			return cmn.ErrNotExist
		}
		p.swapEnumField(origin, localeItem.Field, swapTable)
	}
	return nil
}
