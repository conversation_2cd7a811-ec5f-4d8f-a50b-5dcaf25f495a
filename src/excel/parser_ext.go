package excel

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"tcloud/wolong/cmn"
	"tcloud/wolong/conf"
	"tcloud/wolong/deploy"
	"tcloud/wolong/model"
	"tcloud/wolong/utils"
	"tcloud/wolong/utils/checker"
	"tcloud/wolong/utils/git"
	"tcloud/wolong/utils/plog"
	"time"

	"github.com/spf13/cast"
)

// //////////////////////////////////// 子函数 //////////////////////////////////////
// 解析xlsx文件
func (p *Parser) parseXlsx(channelID int32, tableName string, info *XlsxInfo) (error, map[int]interface{}) {
	xlsxConf := conf.Cfg.Tables[tableName]
	totalStructs := make(map[int]interface{})
	// 创建数据结构
	plog.Info("******************** 解析开始 ********************\n")
	for subTableName, rows := range info.Rows {
		// 先处理单表
		plog.Infof("%d 解析 %v.%v\n", channelID, tableName, subTableName)

		// 替换字段枚举 枚举列
		if err := p.swapEnum(channelID, rows, xlsxConf.Enums); err != nil {
			plog.Error(tableName, "替换枚举出错", err)
			return err, nil
		}

		plog.Infof("%d 解析步骤： %v.%v - 替换枚举 完成\n", channelID, tableName, subTableName)

		// TODO 多语言

		// 参数展开
		if err := p.expandParam(rows); err != nil {
			plog.Error(tableName, "参数展开出错", err)
			return err, nil
		}
		// ...其他展开

		// 配置检查
		if err := p.validator(channelID, rows, tableName, subTableName); err != nil {
			plog.Error(tableName, "参数检查失败", err)
			return err, nil
		}

		// 创建数据结构, 赋值
		isConst := p.ConstantDict[tableName]
		data, err := p.createStruct(rows, tableName)
		if err != nil {
			return err, nil
		}
		plog.Infof("%d 解析 %v.%v - 创建数据结构 完成\n", channelID, tableName, subTableName)

		// id检查, id是不允许一样的
		if !isConst {
			for _, otherTableDict := range p.Output {
				for id := range otherTableDict {
					if data[id] != nil && id != -1 {
						plog.Error(tableName, "ID冲突 id=", id)
					}
				}
			}
		}

		plog.Infof("%d 解析 %v.%v - ID检查 完成\n", channelID, tableName, subTableName)

		if err == nil {
			utils.MergeMap(totalStructs, data)
		}

		plog.Infof("%d 解析 %v.%v - MergeMap 完成\n", channelID, tableName, subTableName)
	}
	// p.Output[tableName] = totalStructs
	return nil, totalStructs
}

// 参数展开
func (p *Parser) expandParam(rows [][]string) error {
	// todo
	return nil
}

// 创建数据结构, map->struct, id作为索引
func (p *Parser) createStruct(rows [][]string, tableName string) (map[int]interface{}, error) {
	data := make(map[int]interface{})
	if len(rows) < conf.SysCfg.IgnoreLine {
		plog.Errorf("错误xlsx数据格式，表头只有%v行，不足%v行\n", len(rows), conf.SysCfg.IgnoreLine)
		return nil, cmn.ErrNull
	}

	isConstantTable := p.ConstantDict[tableName]
	isMissRepeat := p.MissChkRepSheet[tableName]

	builder := NewStructBuilder(p.snakeCase, rows[:conf.SysCfg.IgnoreLine]) // 头部信息构造器
	if err := builder.BuildStruct(); err != nil {
		return nil, err
	}
	// b := NewGoCodeBuilder("", "", "")
	// b.DebugType(builder.StructType, "")
	for rowIdx, row := range rows {
		if rowIdx < conf.SysCfg.IgnoreLine {
			// 前四行头信息忽略掉
			continue
		}
		id, value, err := builder.CreateInstance(row, isConstantTable)
		if err != nil {
			plog.Errorf("解析表:%s 第:%d 行数据失败，错误%v\n", tableName, rowIdx, err)
			continue
		}
		if !isMissRepeat && !isConstantTable && data[id] != nil {
			plog.Errorf("解析表:%s 第:%d 行数据ID重复！！！ id = %d\n", tableName, rowIdx, id)
			return nil, fmt.Errorf("解析表:%s 第%d行 数据ID重复！！！ id = %d", tableName, rowIdx, id)
		}
		data[id] = value
	}
	return data, nil
}

// 生成golang桩文件
func (p *Parser) genGolangStub(codeDir string) {
	LoadFuncNames := make([]string, 0, 16)
	for fileName, data := range p.Output {
		if conf.OutputGo(fileName) == false {
			continue
		}
		// 添加调试日志
		plog.Infof("表 %s 的 table_type: %s", fileName, p.TableType[fileName])

		// 修改过滤逻辑：client_only 表不生成 golang 桩代码，shared 和 server_only 表生成
		if p.TableType[fileName] == conf.TableTypeClientOnly {
			plog.Infof("跳过 client_only 表: %s", fileName)
			continue
		}

		for _, v := range data {
			c := NewGoCodeBuilder(codeDir, p.outputDir, fileName)
			isConstTable := p.ConstantDict[fileName]
			if isConstTable {
				plog.Infof("生成常量文件%s", fileName)
			}
			loadFuncName := c.GenStructWithName(v, fileName, isConstTable)
			LoadFuncNames = append(LoadFuncNames, loadFuncName)
			c.Output()
			break
		}
	}

	// 输出api_cfg.go提供公共接口
	// c := NewGoCodeBuilder(codeDir, p.outputDir, "init")
	// _ = c.GenReloadAllFile(LoadFuncNames)
	// // c.GenInit(funcName) 不执行init默认加载，虽然方便，但会给日常使用造成巨大的负担，日常的结构引用并不需要加载dbc的那些数据文件，用到的自然会去初始化dbc模块
	// c.Output()
}

// 生成C#桩文件
func (p *Parser) genCsharpStub(codeDir string) {
	for fileName, data := range p.Output {
		if conf.OutputCs(fileName) == false {
			continue
		}
		// 修改过滤逻辑：client_only 和 shared 表生成 C# 桩代码
		tableType := p.TableType[fileName]
		if tableType != conf.TableTypeClientOnly && tableType != conf.TableTypeShared && tableType != "" {
			continue
		}
		for _, v := range data {
			var csFileName = cmn.CamelName(fileName)
			c := NewCsharpCodeBuilder(codeDir, p.outputDir, csFileName)
			c.GenStructWithName(v, csFileName)
			c.Output()
			break
		}
	}
}

func (p *Parser) createChannelDirectory(channelID int32) (string, error) {
	channelPath := fmt.Sprintf("%s/%d/%d", p.outputDir, conf.SysCfg.ProductId, channelID)
	errFold := utils.PathCheckDone(channelPath)
	if errFold != nil {
		return "", errFold
	}
	return channelPath, nil
}

func (p *Parser) initUploadInfo(channelID int32, channelList []string) model.UploadInfo {
	serialNoNow := fmt.Sprintf("%d-%s", channelID, time.Now().Format("20060102150405"))
	uploadInfo := model.UploadInfo{}
	uploadInfo.SerialNo = serialNoNow
	uploadInfo.EndpointName, _ = os.Hostname()
	uploadInfo.EnvType = git.GetEnvTypeByGitBranch()
	uploadInfo.ProductId = conf.SysCfg.ProductId
	uploadInfo.ChannelId = channelID
	uploadInfo.GitName, _ = git.GetGitUserName()
	uploadInfo.GitLog, _ = git.GetLatestCommit()
	uploadInfo.ClientIp = utils.LocalIP()
	uploadInfo.RowCnt = len(p.Output) * len(channelList) // 总行数
	uploadInfo.ChannelStr = strings.Join(channelList, ",")
	return uploadInfo
}

func (p *Parser) processTable(channelID int32, channelPath string, tableName string,
	outputData map[int]interface{},
	uploadInfo *model.UploadInfo, cnt int) error {

	filePath := fmt.Sprintf("%s/%s.json", channelPath, tableName)
	outputFile, err := os.Create(filePath)
	if err != nil {
		plog.Error(tableName, "生成文件失败", err)
		return err
	}
	defer outputFile.Close()

	// 判断是否有渠道配置数据
	if p.ChannelXlsx[channelID] != nil && p.ChannelXlsx[channelID][tableName] != nil {
		errParse, totalStructs := p.parseXlsx(channelID, tableName, p.ChannelXlsx[channelID][tableName])
		if errParse != nil {
			plog.Errorf("parseXlsx渠道数据失败 %v", errParse)
			return errParse
		}
		outputData = totalStructs
	}

	var realOutput []byte

	if p.ConstantDict[tableName] {
		bsObj, err := json.Marshal(outputData[0])
		if err != nil {
			plog.Error(tableName, "json.Marshal fail!!", err)
			return err
		}
		str, _ := utils.RemoveJSONField(string(bsObj), "id")
		realOutput = []byte(str)
	} else {
		bs, err := json.Marshal(outputData)
		if err != nil {
			plog.Error(tableName, "json.Marshal fail!!", err)
			return err
		}
		realOutput = bs
	}

	xlsxName := conf.Cfg.Tables[tableName].Workbook
	sheetUp := &model.ExcelSheet{
		FileName:  xlsxName,
		SheetName: tableName,
		Md5:       git.Json2MD5Hex(realOutput),
		CtxJson:   string(realOutput),
	}
	uploadInfo.ExcelSheet = sheetUp
	uploadInfo.RowIndex = cnt // 当前数据行数
	uploadInfo.TableType = p.TableType[tableName]

	if _, err = outputFile.Write(realOutput); err != nil {
		plog.Error(tableName, "fail to file.Write!!", err)
		return err
	}

	if p.consul {
		deploy.Publish2Consul(channelID, tableName, realOutput)
		plog.Infof("导入到Consul成功:%s", conf.SysCfg.ConsulAddress)
	}

	return nil
}

func (p *Parser) uploadToPlatform(uploadList []model.UploadInfo) {
	if len(uploadList) > 0 {
		envName, _ := git.GetCurrentBranch()
		plog.Infof("开始上传到platform，产品ID：%d，渠道：%d，环境：%s\n", uploadList[0].ChannelId, uploadList[0].ProductId, envName)
		deploy.Upload2Platform(uploadList)
	}
}

func (p *Parser) outputJson() {
	// 获取渠道列表
	var channelList []string
	for _, info := range conf.SysCfg.Channel {
		channelList = append(channelList, strconv.Itoa(int(info.Id)))
	}

	cnt := 0
	for _, info := range conf.SysCfg.Channel {
		channelID := info.Id
		channelName := info.Name

		channelPath, err := p.createChannelDirectory(channelID)
		if err != nil {
			plog.Errorf("channelPath create fail : %v", err)
			return
		}

		plog.Infof("输出渠道（%s-%d）配置文件", channelName, channelID)

		uploadInfo := p.initUploadInfo(channelID, channelList)
		uploadList := make([]model.UploadInfo, 0, len(p.Output))

		for tableName, outputData := range p.Output {
			cnt++
			if conf.OutputJson(tableName) == false {
				continue
			}

			if err = p.processTable(channelID, channelPath, tableName, outputData, &uploadInfo, cnt); err != nil {
				plog.Errorf("处理表格 %s 失败: %v", tableName, err)
				return
			}
			if p.plt {
				uploadList = append(uploadList, uploadInfo)
			}
		}

		p.uploadToPlatform(uploadList)
	}
}

// validator 根据 p.CheckInfo 对 xlsx 数据进行校验
func (p *Parser) validator(channelId int32, rows [][]string, tableName, sheetName string) error {
	plog.Infof("validator channelID=%d tableName=%s sheetName=%s\n", channelId, tableName, sheetName)

	ck := p.CheckInfo[channelId]
	checkInfos, exists := ck.CheckFields[sheetName]
	if !exists {
		return nil
	}

	// 转换成列
	columns := TransposeRows(rows)
	for field, checkField := range checkInfos {
		for _, ruleID := range checkField.FieldRuleID {
			checkRule, exists := ck.CheckRules[ruleID]
			if !exists {
				return fmt.Errorf("rule ID %d not found for field %s in table %s", ruleID, field, tableName)
			}

			for _, checkID := range checkRule.CheckId {
				check, exists := ck.Checks[checkID]
				if !exists {
					return fmt.Errorf("check ID %d not found for rule ID %d in table %s", checkID, ruleID, tableName)
				}
				plog.Infof("validator start checkid=%d tableName=%s,field=%s\n", checkID, tableName, field)
				// 外键处理
				fkColumns, err := genForeignKeyColumns(check, channelId, p)
				if err != nil {
					return err
				}
				// 检查
				err = checker.ValidateField(check, tableName, field, columns[field], fkColumns)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// genForeignKeyColumns 生成外键列
func genForeignKeyColumns(ck model.Check, channelID int32, parser *Parser) ([]string, error) {
	if ck.CheckType != checker.CheckTypeForeignKey {
		return []string{}, nil
	}

	// 解析参数 JSON
	var paramMap map[string]interface{}
	err := json.Unmarshal([]byte(ck.ParamJson), &paramMap)
	if err != nil {
		return nil, fmt.Errorf("解析检查 ID %d 的参数 JSON 失败: %v", ck.CheckID, err)
	}

	tableName := cast.ToString(paramMap["table"])
	sheetName := cast.ToString(paramMap["sheet"])
	fieldName := cast.ToString(paramMap["field"])
	xlsxInfo := parser.ChannelXlsx[channelID][tableName]
	if xlsxInfo == nil {
		// channelXlsx 没找到代表和主文件没差异使用 xlsx
		xlsxInfo = parser.Xlsx[tableName]
		if xlsxInfo == nil {
			return nil, fmt.Errorf("生成外键列失败 channelID %d tableName %s not found", channelID, tableName)
		}
	}

	fkColumns := TransposeRows(xlsxInfo.Rows[sheetName])
	if fkColumns == nil {
		return nil, fmt.Errorf("sheet %s not found in xlsxInfo", sheetName)
	}

	return fkColumns[fieldName], nil
}

// TransposeRows 将二维切片 rows 翻转成 map[string][]string，其中键是列索引，值是该列的所有元素
func TransposeRows(rows [][]string) map[string][]string {
	if len(rows) == 0 {
		return map[string][]string{}
	}

	// 获取列数
	numColumns := len(rows[0])
	transposed := make(map[string][]string, numColumns)

	for rowIdx, row := range rows {
		if rowIdx < conf.SysCfg.IgnoreLine {
			// 前四行头信息忽略掉
			continue
		}
		for colIndex, value := range row {
			transposed[rows[1][colIndex]] = append(transposed[rows[1][colIndex]], value)
		}
	}
	return transposed
}
