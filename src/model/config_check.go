package model

// ConfigCheckGetReq 表示获取配置检查页面的请求结构体。
// 它包括环境类型、产品ID和渠道ID。
type ConfigCheckGetReq struct {
	EnvType    int    `json:"env_type"`    // 环境类型，用于区分不同的环境。
	ProductID  int    `json:"product_id"`  // 产品ID，用于标识产品。
	ChannelIDs string `json:"channel_ids"` // 渠道ID列表，用于指定多个渠道。
}

// ConfigCheckGetRsp 表示配置检查的响应结构体。
type ConfigCheckGetRsp struct {
	PltRsp
	Data struct {
		ConfigChecks map[int32]ConfigCheck `json:"config_checks"` // channel_id -> ConfigCheck
	} `json:"data"`
}

// ConfigCheck 配置检查的详细信息。
type ConfigCheck struct {
	CheckFields map[string]map[string]CheckField `json:"check_fields"` // sheet -> field -> CheckField
	CheckRules  map[int]CheckRule                `json:"check_rules"`  // rules_id -> CheckRule
	Checks      map[int]Check                    `json:"checks"`       // check_id -> Check
}

// CheckField 表示单个检查字段的详细信息。
type CheckField struct {
	Sheet       string `json:"sheet"`           // 工作表名称，用于标识配置文件中的工作表。
	Field       string `json:"field"`           // 字段名称，用于标识工作表中的具体字段。
	FieldName   string `json:"field_name"`      // 字段显示名称，用于前端展示。
	FieldType   string `json:"field_type"`      // 字段类型，例如字符串、整数等。
	FieldRuleID []int  `json:"field_rule_list"` // 字段规则ID列表，用于关联具体的检查规则。
}

// CheckRule 表示单个检查规则的详细信息。
type CheckRule struct {
	RuleID   int    `json:"rule_id"`   // 规则ID，唯一标识一个检查规则。
	RuleName string `json:"rule_name"` // 规则名称，用于描述规则的功能。
	RuleType string `json:"rule_type"` // 规则类型，例如必填、长度限制等。
	CheckId  []int  `json:"check_id"`  // 检查ID，用于关联具体的检查项。
}

// Check 表示单个检查项的详细信息。
type Check struct {
	CheckID   int    `json:"check_id"`   // 检查项ID，唯一标识一个检查项。
	CheckName string `json:"check_name"` // 检查规则名称，用于描述检查项的功能。
	CheckType int32  `json:"check_type"` // 检查规则类型，例如字符串检查、数字检查等。
	ParamJson string `json:"param_json"` // 检查规则参数JSON字符串，用于传递检查项的具体参数。
}
