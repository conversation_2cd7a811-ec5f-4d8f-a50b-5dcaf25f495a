# wolong
卧龙：配置配套工具。

# 功能
* excel配置
* 导出json文件，上传到Consul

# 使用
执行如下命令：
wolong_confd_win.exe -json -consul -ftp -go

-config：配置文件，默认为config.toml
-config_system：系统配置文件所在目录，默认为当前目录
-consul：上传到Consul
-json：生成json文件
-output：输出目录，默认为当前目录
-go：生成Go代码
-ftp：上传到FTP


# 需求
1. enum提取，定义配置常量，避免配置错误。
2. 可选的合表/不合表。比如 装备子类：刀，剑，鞋。从策划角度，最好能放同一Excel不同Sheet，但对程序来说，最好能合表。
3. 代码生成。
4. 指定水平读取，支持横行，纵向配置。
5. 统一config.toml作为全局配置文件，作为清单文件。
6. 基本的错误检查。
7. 参数展开，支持{0}, {1}等占位符。
8. 多语言支持

# 局限性（For程序）
1. 不支持slice和map多级结构。简单结构或单层struct，不深度递归配置，降低复杂度。
2. 允许多层struct，但不允许多层struct的member分散出现，也不允许struct内部包含slice或map。
3. slice和map，名称为单一字符串如[awake]或驼峰字符串如[awakeMaterial]否则读取会失败
4. enum.xlsx枚举表特殊，没有复杂结构, 因此第四行不需要留白，可存储数据

# 表达式
* \<struct> 尖括号用于表达子struct
* [slice] 方括号用于表达slice
* {map} 大括号用于表达map
* \- 横杠用于略去解析（多用于备注，说明表单内容，但不输出）
* 同一slice/map的不同member，使用[slice]#1 字段描述，比如slice类型有type和value两个字段，则这两个字段的desc栏都填[slice]#1, 第二个value的desc栏填[slice]#2
* 使用segment 表达式，在单元格配置"|"切割数组，不做任何检查切割为字符串，如：1|2
* remark 用于描述，不输出

# 代码生成规则
* 使用字段名的全小写输出为json字段，使用此字段的驼峰式作为字段类名.
* 比如spinType, 输出的json文件类型为"spintype"，输出的类成员名为Spintype

# 配置文件配置项
* workbook   string     // 工作簿
* sheet      []string   // 子表
* horizontal bool       // 是否水平解析 默认false
* output     []string   // 输出类型选项 json, cs, go. 默认全输出
* constant   bool       // 常量标识，标识本表为常量配置，只取第一条
* type       string     // 表类型 server_only; client_local_read
* duplicate  bool       // 重复结构, 设置为true, 避免重复解析出现的校验错误
* enums      []EnumItem // 枚举替换
* MissRepeat bool       // 去掉单个表格重复ID校验, 为true的时候不校验

# 配置文件详解
## 表头
1. 表头一共有四行，为默认
第一行：字段类型行
只限于以下：
BOOL，UINT，INT，INT32，INT64，STRING，FLOAT，DOUBLE
2. 第二行：字段名行
3. 第三行：字段备注行
4. 第四行：表达式行，如没有特殊字段，则空
eg：配置奖励数组
item有多行，item_id,count分别为列字段，可配置如下：
｜item_id ｜count   ｜item_id ｜count   ｜
｜[item]#1｜[item]#1｜[item]#2｜[item]#2｜
5. 固定头
![img.png](docs/img_head.png)
6. 如图，每个表固定结构

## 配置枚举
enum.xlsx 用来配置枚举
枚举值的配置，对于策划来讲非常不友好，展示为英文字段名，比较好阅读。
eg：item.xlsx为物品表,如图商品表配置item_id的时候，直接配置字段名
![配置的枚举值](docs/enum1.png)

## 配置主键枚举
业务模块用到其它值的主键，关联其它配置表，这时候可以用另外一个表的name字段来配置，
而需要去配置该配置的ID，直接将英文名字配置到主键字段上。实际解析后，json里面存的是这个关联ID。
后面方便根据这个ID去查询配置表。
这里约定子表需要配置id和name字段，id字段作为主键，name字段作为枚举值。
config.toml中配置如下：
```toml
[tables.goods]
sheet = ["商品"]
workbook = "xlsx/商城表.xlsx"
enums = [
     {field = "item_id", table = "item"}
 ]
```
导出到程序的时候变成约定数值
![导出后枚举值](docs/img_enum2.png)

## 配置特殊结构
在表达式行
使用[object]#1, [object]#2, [object]#3等，表示数组，数组的元素类型为field的类型。
## 配置Map
在表达式行
使用{object}表示map，map的key类型为field的类型。
## 配置结构体
在表达式行
使用<object>表示struct，struct的成员类型为field的类型。

## 水平展开配置说明
竖着配置的时候，id行统一配置 -1，则忽略。做为常量配置。只有一行。
Object类型的Json格式。

## 输出
1. 导出json为数据
2. 生成不同语言的桩代码，来读取解析配置
3. 一张工作簿对应一个json文件