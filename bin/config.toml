[tables]
[tables.global]
sheet = ["全局配置"]
workbook = "全局配置表.xlsx"
constant = true
horizontal = true

# 网关封杀配置
[tables.gate_info]
sheet = ["GateInfo"]
workbook = "全局配置表.xlsx"
constant = true
horizontal = true

# 动态路由配置
[tables.route_key]
sheet = ["RouteKey"]
workbook = "全局配置表.xlsx"

# 多语言信息表
[tables.language_info]
sheet = ["LanguageInfo"]
workbook = "language_info.xlsx"
enums = [
]

## 物品相关配置
#[tables.item]
#sheet = ["物品表"]
#workbook = "物品表.xlsx"
#enums = [
#]
#
#[tables.goods]
#sheet = ["商品"]
#workbook = "商城表.xlsx"
#enums = [
#    {field = "item_id", table = "item"}
#]
#
#[tables.app_update_info]
#sheet = ["AppUpdateInfo"]
#workbook = "AppInfo.xlsx"
#constant = true # 标注为常量表
#horizontal = true
#
#[tables.app_resource_info]
#sheet = ["AppResourceInfo"]
#workbook = "AppInfo.xlsx"
#constant = true # 标注为常量表
#horizontal = true
#
#[tables.app_address_info]
#sheet = ["AppAddressInfo"]
#workbook = "AppInfo.xlsx"
#constant = true # 标注为常量表
#horizontal = true
#
## 天气配置
#[tables.weather_Const]
#sheet = ["WeatherConst"]
#workbook = "Weather.xlsx"
#constant = true # 标注为常量表
#horizontal = true
#
#[tables.weather_conditions]
#sheet = ["WeatherConditions"]
#workbook = "Weather.xlsx"
#enums = [
#]
#
#[tables.weather_weight]
#sheet = ["WeatherWeight"]
#workbook = "Weather.xlsx"
#enums = [
#    {field = "weather_id", table = "weather_conditions"}
#]
#
## Basic鱼配置
#[tables.basic_fish_Const]
#sheet = ["BasicFishConst"]
#workbook = "basic_鱼基础表.xlsx"
#constant = true # 标注为常量表
#horizontal = true
#
#[tables.basic_fish_species]
#sheet = ["BasicFishSpecies"]
#workbook = "basic_鱼基础表.xlsx"
#enums = [
#]
#
#[tables.basic_fish_quality]
#sheet = ["BasicFishQuality"]
#workbook = "basic_鱼基础表.xlsx"
#enums = [
#]
#
#[tables.basic_fish_character]
#sheet = ["BasicFishCharacter"]
#workbook = "basic_鱼基础表.xlsx"
#enums = [
#]
#
#[tables.basic_fish_habitus]
#sheet = ["BasicFishHabitus"]
#workbook = "basic_鱼基础表.xlsx"
#enums = [
##    {field = "preferred_weather", table = "weather_conditions"}
#]
